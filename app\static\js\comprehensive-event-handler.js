/**
 * 全面的安全事件处理器
 * 处理所有类型的安全关键事件
 */


    // 安全执行代码的函数（替代eval）
    function safeExecuteCode(code) {
        if (!code) return;

        code = code.trim();

        console.log('🔍 comprehensive-event-handler 尝试执行代码:', code);

        // 处理页面跳转
        if (code.includes('window.location.href=') || code.includes('location.href=')) {
            const url = code.split('=')[1].replace(/['"]/g, '').trim();
            console.log('✅ 执行页面跳转:', url);
            window.location.href = url;
            return true;
        }

        // 处理confirm调用 - 改进的正则表达式
        if (code.includes('confirm(')) {
            console.log('🔍 检测到confirm调用:', code);

            // 多种confirm调用格式的正则表达式
            const confirmPatterns = [
                /confirm\s*\(\s*['"](.*?)['"]\s*\)/,  // confirm('message')
                /confirm\s*\(\s*"([^"]*)"\s*\)/,      // confirm("message")
                /confirm\s*\(\s*'([^']*)'\s*\)/,      // confirm('message')
                /confirm\s*\(\s*([^)]+)\s*\)/         // confirm(variable)
            ];

            for (let pattern of confirmPatterns) {
                const match = code.match(pattern);
                if (match) {
                    const message = match[1] || '确定要执行此操作吗？';
                    console.log('✅ 执行confirm，消息:', message);
                    return confirm(message);
                }
            }

            // 如果没有匹配到参数，使用默认消息
            console.log('⚠️ confirm调用格式不标准，使用默认消息');
            return confirm('确定要执行此操作吗？');
        }

        // 处理消费计划确认函数
        if (code.startsWith('executeConfirm(')) {
            const urlMatch = code.match(/executeConfirm\(['"]([^'"]+)['"]\)/);
            if (urlMatch) {
                const url = urlMatch[1];
                if (confirm('确定要执行该消耗计划吗？执行后将自动生成出库单。')) {
                    console.log('✅ 执行消费计划:', url);
                    // 创建表单并提交POST请求
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;

                    // 添加CSRF token
                    const csrfToken = document.querySelector('meta[name=csrf-token]');
                    if (csrfToken) {
                        const csrfInput = document.createElement('input');
                        csrfInput.type = 'hidden';
                        csrfInput.name = 'csrf_token';
                        csrfInput.value = csrfToken.getAttribute('content');
                        form.appendChild(csrfInput);
                    }

                    document.body.appendChild(form);
                    form.submit();
                }
                return true;
            }
        }

        if (code.startsWith('approveConfirm(')) {
            const urlMatch = code.match(/approveConfirm\(['"]([^'"]+)['"]\)/);
            if (urlMatch) {
                const url = urlMatch[1];
                if (confirm('确定要审核该消耗计划吗？')) {
                    console.log('✅ 审核消费计划:', url);
                    // 创建表单并提交POST请求
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;

                    // 添加CSRF token
                    const csrfToken = document.querySelector('meta[name=csrf-token]');
                    if (csrfToken) {
                        const csrfInput = document.createElement('input');
                        csrfInput.type = 'hidden';
                        csrfInput.name = 'csrf_token';
                        csrfInput.value = csrfToken.getAttribute('content');
                        form.appendChild(csrfInput);
                    }

                    document.body.appendChild(form);
                    form.submit();
                }
                return true;
            }
        }

        if (code.startsWith('cancelConfirm(')) {
            const urlMatch = code.match(/cancelConfirm\(['"]([^'"]+)['"]\)/);
            if (urlMatch) {
                const url = urlMatch[1];
                if (confirm('确定要取消该消耗计划吗？此操作不可撤销。')) {
                    console.log('✅ 取消消费计划:', url);
                    // 创建表单并提交POST请求
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;

                    // 添加CSRF token
                    const csrfToken = document.querySelector('meta[name=csrf-token]');
                    if (csrfToken) {
                        const csrfInput = document.createElement('input');
                        csrfInput.type = 'hidden';
                        csrfInput.name = 'csrf_token';
                        csrfInput.value = csrfToken.getAttribute('content');
                        form.appendChild(csrfInput);
                    }

                    document.body.appendChild(form);
                    form.submit();
                }
                return true;
            }
        }

        // 处理简单的函数调用
        const funcMatch = code.match(/^(\w+)\s*\(([^)]*)\)$/);
        if (funcMatch) {
            const funcName = funcMatch[1];
            const args = funcMatch[2];
            console.log('🔍 检测到函数调用:', funcName, '参数:', args);

            if (typeof window[funcName] === 'function') {
                try {
                    // 解析参数
                    if (args.trim()) {
                        // 简单的字符串参数解析
                        const cleanArgs = args.replace(/['"]/g, '').trim();
                        console.log('✅ 执行全局函数:', funcName, '参数:', cleanArgs);
                        return window[funcName](cleanArgs);
                    } else {
                        console.log('✅ 执行全局函数:', funcName, '无参数');
                        return window[funcName]();
                    }
                } catch (e) {
                    console.warn('函数执行失败:', funcName, e);
                }
            }
        }

        // 处理其他安全操作
        if (code === 'window.print()') {
            console.log('✅ 执行window.print()');
            window.print();
            return true;
        }
        if (code === 'history.back()') {
            console.log('✅ 执行history.back()');
            history.back();
            return true;
        }
        if (code === 'location.reload()') {
            console.log('✅ 执行location.reload()');
            location.reload();
            return true;
        }

        console.warn('❌ 不支持的代码执行:', code);
        return false;
    }

    


    document.addEventListener('DOMContentLoaded', function() {
    
    // 处理安全确认操作
    document.querySelectorAll('[data-action="safe-confirm"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const confirmCode = this.getAttribute('data-confirm-code');
            
            try {
                // 安全执行确认代码
                const result = safeExecuteCode(confirmCode);
                console.log('确认操作执行结果:', result);
            } catch (error) {
                console.error('确认操作执行失败:', error);
                alert('操作失败，请重试');
            }
        });
    });
    
    // 处理安全删除操作
    document.querySelectorAll('[data-action="safe-delete"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const deleteCode = this.getAttribute('data-delete-code');
            const confirmMessage = this.getAttribute('data-confirm-message') || '确定要删除吗？';
            
            if (confirm(confirmMessage)) {
                try {
                    safeExecuteCode(deleteCode);
                } catch (error) {
                    console.error('删除操作执行失败:', error);
                    alert('删除失败，请重试');
                }
            }
        });
    });
    
    // 处理复杂确认操作
    document.querySelectorAll('[data-action="complex-confirm"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const complexCode = this.getAttribute('data-complex-code');
            
            try {
                // 对于复杂的确认逻辑，直接执行
                safeExecuteCode(complexCode);
            } catch (error) {
                console.error('复杂确认操作执行失败:', error);
                alert('操作失败，请重试');
            }
        });
    });
    
    // 处理安全提交操作
    document.querySelectorAll('[data-action="safe-submit"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const submitCode = this.getAttribute('data-submit-code');
            
            try {
                safeExecuteCode(submitCode);
            } catch (error) {
                console.error('提交操作执行失败:', error);
                alert('提交失败，请检查输入');
            }
        });
    });
    
    // 处理安全导航操作
    document.querySelectorAll('[data-action="safe-navigate"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const navigateCode = this.getAttribute('data-navigate-code');
            
            try {
                safeExecuteCode(navigateCode);
            } catch (error) {
                console.error('导航操作执行失败:', error);
                alert('页面跳转失败');
            }
        });
    });
    
    // 处理通用安全执行
    document.querySelectorAll('[data-action="safe-execute"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            
            const executeCode = this.getAttribute('data-execute-code');
            
            try {
                safeExecuteCode(executeCode);
            } catch (error) {
                console.error('代码执行失败:', error);
            }
        });
    });
    
    // 处理data-onclick属性（消费计划页面使用）
    document.querySelectorAll('[data-onclick]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();

            const onclickCode = this.getAttribute('data-onclick');

            try {
                console.log('🔍 处理data-onclick:', onclickCode);
                safeExecuteCode(onclickCode);
            } catch (error) {
                console.error('data-onclick执行失败:', error);
            }
        });
    });

    // 兼容之前的所有修复
    bindLegacyHandlers();
    

    
    // 绑定之前的处理器（兼容性）
    function bindLegacyHandlers() {
        // 处理之前修复的各种类型
        const legacySelectors = [
            '[data-action="critical-confirm"]',
            '[data-action="delete-confirm"]',
            '[data-validation="critical"]',
            '[data-validation="true"]',
            '.print-button',
            '.back-button',
            '.reload-button'
        ];
        
        legacySelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                if (!element.hasAttribute('data-legacy-bound')) {
                    bindLegacyElement(element, selector);
                    element.setAttribute('data-legacy-bound', 'true');
                }
            });
        });
    }
    
    function bindLegacyElement(element, selector) {
        if (selector.includes('print-button')) {
            element.addEventListener('click', () => window.print());
        } else if (selector.includes('back-button')) {
            element.addEventListener('click', () => history.back());
        } else if (selector.includes('reload-button')) {
            element.addEventListener('click', () => location.reload());
        }
        // 其他类型的处理器已经在之前的脚本中定义
    }
    
    console.log('✅ 全面的安全事件处理器已加载');
});