2025-06-02 23:18:41,110 INFO: 副表数据: 日期=3, 餐次=早餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,116 INFO: 副表数据: 日期=3, 餐次=早餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,117 INFO: 副表数据: 日期=3, 餐次=午餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,117 INFO: 副表数据: 日期=3, 餐次=午餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,118 INFO: 副表数据: 日期=3, 餐次=晚餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,118 INFO: 副表数据: 日期=3, 餐次=晚餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,119 INFO: 副表数据: 日期=3, 餐次=晚餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,119 INFO: 副表数据: 日期=3, 餐次=晚餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,119 INFO: 副表数据: 日期=3, 餐次=晚餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,120 INFO: 副表数据: 日期=3, 餐次=晚餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,120 INFO: 副表数据: 日期=3, 餐次=晚餐, 菜品=🏫 鲜椒青瓜干, ID=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,120 INFO: 副表数据: 日期=4, 餐次=早餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,121 INFO: 副表数据: 日期=4, 餐次=早餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,121 INFO: 副表数据: 日期=4, 餐次=早餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,121 INFO: 副表数据: 日期=4, 餐次=早餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,122 INFO: 副表数据: 日期=4, 餐次=早餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,122 INFO: 副表数据: 日期=4, 餐次=早餐, 菜品=🏫 鲜椒青瓜干, ID=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,123 INFO: 副表数据: 日期=4, 餐次=早餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,123 INFO: 副表数据: 日期=4, 餐次=午餐, 菜品=🏫 鲜椒青瓜干, ID=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,123 INFO: 副表数据: 日期=4, 餐次=午餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,124 INFO: 副表数据: 日期=4, 餐次=午餐, 菜品=🏫 黑木耳炒山药, ID=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,124 INFO: 副表数据: 日期=4, 餐次=午餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,124 INFO: 副表数据: 日期=4, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,125 INFO: 副表数据: 日期=4, 餐次=午餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,128 INFO: 副表数据: 日期=4, 餐次=晚餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,129 INFO: 副表数据: 日期=4, 餐次=晚餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,129 INFO: 副表数据: 日期=5, 餐次=早餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,129 INFO: 副表数据: 日期=5, 餐次=午餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,130 INFO: 副表数据: 日期=5, 餐次=晚餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,130 INFO: 副表数据: 日期=6, 餐次=早餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,130 INFO: 副表数据: 日期=6, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,131 INFO: 副表数据: 日期=6, 餐次=晚餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,131 INFO: 副表数据: 日期=7, 餐次=早餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,131 INFO: 副表数据: 日期=7, 餐次=午餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,132 INFO: 副表数据: 日期=7, 餐次=晚餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:323]
2025-06-02 23:18:41,132 INFO: 副表数据映射构建完成: 7 天, 77 个菜品 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:325]
2025-06-02 23:18:41,132 WARNING: 跳过无效日期: 1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:335]
2025-06-02 23:18:41,133 WARNING: 跳过无效日期: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:335]
2025-06-02 23:18:41,133 WARNING: 跳过无效日期: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:335]
2025-06-02 23:18:41,133 WARNING: 跳过无效日期: 4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:335]
2025-06-02 23:18:41,134 WARNING: 跳过无效日期: 5 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:335]
2025-06-02 23:18:41,134 WARNING: 跳过无效日期: 6 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:335]
2025-06-02 23:18:41,134 WARNING: 跳过无效日期: 7 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:335]
2025-06-02 23:18:41,135 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 鲜椒青瓜干 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,135 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 鲜椒青瓜干, ID=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,135 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,135 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,136 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 西瓜桃子面 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,136 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
