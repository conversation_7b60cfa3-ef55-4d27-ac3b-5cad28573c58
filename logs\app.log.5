2025-06-03 10:16:12,471 INFO:   consumption_date: 2025-06-03 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:205]
2025-06-03 10:16:12,475 INFO:   meal_types: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:206]
2025-06-03 10:16:12,476 INFO:   diners_count: 1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:207]
2025-06-03 10:16:12,476 INFO:   notes:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:208]
2025-06-03 10:16:12,506 INFO: 创建餐次 早餐 的消耗计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:237]
2025-06-03 10:16:12,516 INFO: 未找到对应的菜单计划，将创建独立的消耗计划: area_id=42, date=2025-06-03, meal_type=早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:253]
2025-06-03 10:16:12,645 INFO: 创建了消耗计划 ID: 29 (餐次: 早餐) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:279]
2025-06-03 10:16:12,828 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 10:16:12,829 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 10:16:12,831 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 10:16:12,831 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 10:16:55,408 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 10:16:55,408 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 10:16:55,437 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 10:16:55,438 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 10:17:02,915 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 10:17:02,915 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 10:17:02,919 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 10:17:02,920 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 10:17:08,831 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:342]
2025-06-03 10:17:08,835 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:360]
2025-06-03 10:17:08,835 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 10:17:08,842 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:374]
2025-06-03 10:17:08,845 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:378]
2025-06-03 10:17:08,846 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:434]
2025-06-03 10:17:08,846 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:440]
2025-06-03 10:17:08,852 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:449]
2025-06-03 10:17:08,877 INFO: 周菜单 37 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:460]
2025-06-03 10:17:08,887 INFO: 分析食谱: 🏫 鲜椒青瓜干, recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:17:08,910 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:17:08,990 INFO: 分析食谱: 🏫 黄米南瓜盅, recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:17:08,993 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:17:09,022 INFO: 分析食谱: 🏫 黑木耳炒山药, recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:17:09,023 INFO: 找到 7 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:17:09,034 INFO: 分析食谱: 🏫 鲜笋烧仔排, recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:17:09,052 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:17:09,062 INFO: 分析食谱: 🏫 鲜蚕豆烧大雁, recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:17:09,063 INFO: 找到 5 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:17:09,086 INFO: 分析食谱: 🏫 缤纷吐司蒸, recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:17:09,088 INFO: 找到 4 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:17:09,959 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐', '午餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:342]
2025-06-03 10:17:09,963 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐', '午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:360]
2025-06-03 10:17:09,963 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 10:17:09,969 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:374]
2025-06-03 10:17:09,970 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:378]
2025-06-03 10:17:09,971 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:434]
2025-06-03 10:17:09,971 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:440]
2025-06-03 10:17:09,979 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:449]
2025-06-03 10:17:10,001 INFO: 周菜单 37 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:460]
2025-06-03 10:17:10,009 INFO: 分析食谱: 🏫 鲜椒青瓜干, recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:17:10,027 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:17:10,094 INFO: 分析食谱: 🏫 黄米南瓜盅, recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:17:10,095 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:17:10,121 INFO: 分析食谱: 🏫 黑木耳炒山药, recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:17:10,122 INFO: 找到 7 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:17:10,150 INFO: 分析食谱: 🏫 鲜笋烧仔排, recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:17:10,152 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:17:10,181 INFO: 分析食谱: 🏫 鲜蚕豆烧大雁, recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
