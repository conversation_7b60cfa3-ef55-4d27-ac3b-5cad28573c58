2025-06-02 23:18:41,161 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,167 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 鲜笋烧仔排 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,167 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,167 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 黑木耳炒山药 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,169 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 黑木耳炒山药, ID=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,169 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 缤纷吐司蒸 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,170 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,170 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,171 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,171 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 西瓜桃子面 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,171 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,172 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,172 INFO: 从副表补全recipe_id: 日期=2, 餐次=午餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,172 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 鲜笋烧仔排 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,173 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,173 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,174 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,174 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 西瓜桃子面 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,174 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,178 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,178 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,178 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=晚餐, 菜品=🏫 缤纷吐司蒸 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,179 INFO: 从副表补全recipe_id: 日期=2, 餐次=晚餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,179 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=早餐, 菜品=🏫 鲜笋烧仔排 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,180 INFO: 从副表补全recipe_id: 日期=3, 餐次=早餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,180 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=早餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,180 INFO: 从副表补全recipe_id: 日期=3, 餐次=早餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,181 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=早餐, 菜品=🏫 黑木耳炒山药 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,181 INFO: 从副表补全recipe_id: 日期=3, 餐次=早餐, 菜品=🏫 黑木耳炒山药, ID=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,181 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=早餐, 菜品=🏫 缤纷吐司蒸 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,182 INFO: 从副表补全recipe_id: 日期=3, 餐次=早餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,182 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=早餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,183 INFO: 从副表补全recipe_id: 日期=3, 餐次=早餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,183 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=早餐, 菜品=🏫 西瓜桃子面 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,183 INFO: 从副表补全recipe_id: 日期=3, 餐次=早餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,184 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=早餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,184 INFO: 从副表补全recipe_id: 日期=3, 餐次=早餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,184 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=午餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,185 INFO: 从副表补全recipe_id: 日期=3, 餐次=午餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,185 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=午餐, 菜品=🏫 西瓜桃子面 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,186 INFO: 从副表补全recipe_id: 日期=3, 餐次=午餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,186 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=晚餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,186 INFO: 从副表补全recipe_id: 日期=3, 餐次=晚餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,191 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=晚餐, 菜品=🏫 鲜笋烧仔排 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,191 INFO: 从副表补全recipe_id: 日期=3, 餐次=晚餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
