2025-06-03 00:41:55,917 ERROR: 撤销入库操作时出错: (pyodbc.IntegrityError) ('23000', '[23000] [Microsoft][ODBC SQL Server Driver][SQL Server]DELETE 语句与 REFERENCE 约束"FK_consumption_details_inventories"冲突。该冲突发生于数据库"StudentsCMSSP"，表"dbo.consumption_details", column \'inventory_id\'。 (547) (SQLExecDirectW); [23000] [Microsoft][ODBC SQL Server Driver][SQL Server]语句已终止。 (3621)')
[SQL: 
                        DELETE FROM inventories WHERE id = ?
                        ]
[parameters: (63,)]
(Background on this error at: https://sqlalche.me/e/20/gkpj) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:692]
2025-06-03 00:50:51,374 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 00:52:23,031 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 00:52:23,032 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 00:52:23,035 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 00:52:23,035 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 00:52:29,295 INFO: 用户 18373062333 (ID: 34) 访问消费计划超级编辑器 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:18]
2025-06-03 00:52:29,297 INFO: 用户区域ID: 42, 用户区域级别: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:19]
2025-06-03 00:52:29,303 INFO: get_current_area() 返回: <AdministrativeArea 42> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:25]
2025-06-03 00:52:29,303 INFO: 最终确定的区域: 朝阳区实验中学 (ID: 42) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:40]
2025-06-03 00:52:29,308 INFO: 查找仓库结果: <Warehouse 4> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:44]
2025-06-03 00:52:29,308 INFO: 找到仓库: 朝阳区实验中学中心仓库 (ID: 4) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:51]
2025-06-03 00:52:29,308 INFO: 准备渲染模板 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:52]
2025-06-03 00:52:57,144 ERROR: 获取库存批次时出错: 'str' object has no attribute 'strftime' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:190]
2025-06-03 00:53:55,740 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 00:54:00,212 INFO: 用户 18373062333 (ID: 34) 访问消费计划超级编辑器 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:18]
2025-06-03 00:54:00,213 INFO: 用户区域ID: 42, 用户区域级别: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:19]
2025-06-03 00:54:00,217 INFO: get_current_area() 返回: <AdministrativeArea 42> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:25]
2025-06-03 00:54:00,217 INFO: 最终确定的区域: 朝阳区实验中学 (ID: 42) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:40]
2025-06-03 00:54:00,245 INFO: 查找仓库结果: <Warehouse 4> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:44]
2025-06-03 00:54:00,246 INFO: 找到仓库: 朝阳区实验中学中心仓库 (ID: 4) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:51]
2025-06-03 00:54:00,246 INFO: 准备渲染模板 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:52]
2025-06-03 00:54:13,796 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:371]
2025-06-03 00:54:13,797 INFO: 解析参数: area_id=42, consumption_date=2025-06-03, meal_types=['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:377]
2025-06-03 00:54:13,801 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:394]
2025-06-03 00:54:13,803 ERROR: 分析食谱时出错: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            -- 从菜单计划获取食谱
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners, 'menu_plan' as source
            FROM menu_plans mp
            JOIN recipes r ON mp.recipe_id = r.id
            WHERE mp.area_id = ?
            AND mp.plan_date = ?
            AND mp.meal_type IN ('早餐')

            UNION ALL

            -- 从周菜单获取食谱
            SELECT r.id, r.name as recipe_name, wmr.meal_type,
                   COALESCE(wm.expected_diners, 0) as expected_diners, 'weekly_menu' as source
            FROM weekly_menus wm
            JOIN weekly_menu_recipes wmr ON wm.id = wmr.weekly_menu_id
            JOIN recipes r ON wmr.recipe_id = r.id
            WHERE wm.area_id = ?
            AND wm.week_start <= ?
            AND wm.week_end >= ?
            AND wmr.day_of_week = ?
            AND wmr.meal_type IN ('早餐')
        ]
[parameters: (42, datetime.date(2025, 6, 3), 42, datetime.date(2025, 6, 3), datetime.date(2025, 6, 3), 3)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:515]
2025-06-03 00:54:16,840 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐', '午餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:371]
2025-06-03 00:54:16,841 INFO: 解析参数: area_id=42, consumption_date=2025-06-03, meal_types=['早餐', '午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:377]
2025-06-03 00:54:16,843 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐', '午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:394]
2025-06-03 00:54:16,844 ERROR: 分析食谱时出错: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            -- 从菜单计划获取食谱
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners, 'menu_plan' as source
            FROM menu_plans mp
            JOIN recipes r ON mp.recipe_id = r.id
            WHERE mp.area_id = ?
            AND mp.plan_date = ?
            AND mp.meal_type IN ('早餐','午餐')

            UNION ALL

            -- 从周菜单获取食谱
            SELECT r.id, r.name as recipe_name, wmr.meal_type,
                   COALESCE(wm.expected_diners, 0) as expected_diners, 'weekly_menu' as source
            FROM weekly_menus wm
            JOIN weekly_menu_recipes wmr ON wm.id = wmr.weekly_menu_id
            JOIN recipes r ON wmr.recipe_id = r.id
            WHERE wm.area_id = ?
            AND wm.week_start <= ?
            AND wm.week_end >= ?
            AND wmr.day_of_week = ?
            AND wmr.meal_type IN ('早餐','午餐')
        ]
[parameters: (42, datetime.date(2025, 6, 3), 42, datetime.date(2025, 6, 3), datetime.date(2025, 6, 3), 3)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:515]
2025-06-03 00:54:17,911 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['午餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:371]
2025-06-03 00:54:17,913 INFO: 解析参数: area_id=42, consumption_date=2025-06-03, meal_types=['午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:377]
2025-06-03 00:54:17,921 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:394]
