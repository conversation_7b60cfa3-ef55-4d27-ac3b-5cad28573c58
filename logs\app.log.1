2025-06-03 10:26:16,229 INFO: 分析食谱: 🏫 缤纷吐司蒸, recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:16,357 INFO: 找到 4 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:53,585 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐', '午餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:342]
2025-06-03 10:26:53,590 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐', '午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:360]
2025-06-03 10:26:53,590 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 10:26:53,600 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:374]
2025-06-03 10:26:53,602 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:378]
2025-06-03 10:26:53,602 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:434]
2025-06-03 10:26:53,602 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:440]
2025-06-03 10:26:53,610 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:449]
2025-06-03 10:26:53,665 INFO: 周菜单 37 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:460]
2025-06-03 10:26:53,693 INFO: 分析食谱: 🏫 鲜椒青瓜干, recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:53,697 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:53,746 INFO: 分析食谱: 🏫 黄米南瓜盅, recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:53,748 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:53,756 INFO: 分析食谱: 🏫 黑木耳炒山药, recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:53,757 INFO: 找到 7 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:53,850 INFO: 分析食谱: 🏫 鲜笋烧仔排, recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:53,856 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:53,934 INFO: 分析食谱: 🏫 鲜蚕豆烧大雁, recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:53,938 INFO: 找到 5 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:53,967 INFO: 分析食谱: 🏫 缤纷吐司蒸, recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:53,969 INFO: 找到 4 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:53,976 INFO: 查询餐次: 午餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 10:26:53,984 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:374]
2025-06-03 10:26:53,988 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:378]
2025-06-03 10:26:53,988 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:434]
2025-06-03 10:26:53,989 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:440]
2025-06-03 10:26:53,997 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:449]
2025-06-03 10:26:54,044 INFO: 周菜单 37 中找到 7 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:460]
2025-06-03 10:26:54,070 INFO: 分析食谱: 🏫 米饭, recipe_id=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:54,092 INFO: 找到 1 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:54,127 INFO: 分析食谱: 🏫 鲜笋烧仔排, recipe_id=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:54,133 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:54,216 INFO: 分析食谱: 🏫 黑木耳炒山药, recipe_id=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:54,220 INFO: 找到 7 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:54,232 INFO: 分析食谱: 🏫 缤纷吐司蒸, recipe_id=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:54,247 INFO: 找到 4 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:54,256 INFO: 分析食谱: 🏫 鲜蚕豆烧大雁, recipe_id=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:54,257 INFO: 找到 5 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:54,280 INFO: 分析食谱: 🏫 西瓜桃子面, recipe_id=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:54,282 INFO: 找到 8 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:54,345 INFO: 分析食谱: 🏫 黄米南瓜盅, recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:54,351 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:56,804 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐', '午餐', '晚餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:342]
2025-06-03 10:26:56,809 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐', '午餐', '晚餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:360]
2025-06-03 10:26:56,809 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 10:26:56,853 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:374]
2025-06-03 10:26:56,856 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:378]
2025-06-03 10:26:56,856 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:434]
2025-06-03 10:26:56,856 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:440]
2025-06-03 10:26:56,877 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:449]
2025-06-03 10:26:56,882 INFO: 周菜单 37 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:460]
2025-06-03 10:26:56,888 INFO: 分析食谱: 🏫 鲜椒青瓜干, recipe_id=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:56,909 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
2025-06-03 10:26:56,948 INFO: 分析食谱: 🏫 黄米南瓜盅, recipe_id=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:468]
2025-06-03 10:26:56,949 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:475]
