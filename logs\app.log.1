2025-06-03 01:43:59,207 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 01:43:59,210 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:384]
2025-06-03 01:43:59,210 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:390]
2025-06-03 01:43:59,299 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:399]
2025-06-03 01:43:59,305 INFO: 周菜单 37 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:410]
2025-06-03 01:44:00,729 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐', '午餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:329]
2025-06-03 01:44:00,734 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐', '午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:347]
2025-06-03 01:44:00,734 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:352]
2025-06-03 01:44:00,739 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:44:00,741 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 01:44:00,741 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:384]
2025-06-03 01:44:00,751 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:390]
2025-06-03 01:44:00,772 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:399]
2025-06-03 01:44:00,985 INFO: 周菜单 37 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:410]
2025-06-03 01:44:00,985 INFO: 查询餐次: 午餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:352]
2025-06-03 01:44:00,992 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:44:01,021 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 01:44:01,021 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:384]
2025-06-03 01:44:01,021 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:390]
2025-06-03 01:44:01,042 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:399]
2025-06-03 01:44:01,044 INFO: 周菜单 37 中找到 7 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:410]
2025-06-03 01:44:01,609 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐', '午餐', '晚餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:329]
2025-06-03 01:44:01,645 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐', '午餐', '晚餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:347]
2025-06-03 01:44:01,645 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:352]
2025-06-03 01:44:01,680 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:44:01,740 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 01:44:01,741 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:384]
2025-06-03 01:44:01,741 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:390]
2025-06-03 01:44:01,761 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:399]
2025-06-03 01:44:01,767 INFO: 周菜单 37 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:410]
2025-06-03 01:44:01,767 INFO: 查询餐次: 午餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:352]
2025-06-03 01:44:01,768 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:44:01,769 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 01:44:01,769 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:384]
2025-06-03 01:44:01,769 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:390]
2025-06-03 01:44:01,770 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:399]
2025-06-03 01:44:01,771 INFO: 周菜单 37 中找到 7 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:410]
2025-06-03 01:44:01,771 INFO: 查询餐次: 晚餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:352]
2025-06-03 01:44:01,772 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:44:01,773 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 01:44:01,773 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:384]
2025-06-03 01:44:01,774 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:390]
2025-06-03 01:44:01,796 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:399]
2025-06-03 01:44:01,797 INFO: 周菜单 37 中找到 5 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:410]
2025-06-03 01:44:43,583 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 01:44:48,400 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:329]
2025-06-03 01:44:48,407 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:347]
2025-06-03 01:44:48,407 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:352]
2025-06-03 01:44:48,415 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:44:48,419 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 01:44:48,419 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:421]
2025-06-03 01:44:48,420 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:427]
2025-06-03 01:44:48,481 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:436]
2025-06-03 01:44:48,570 INFO: 周菜单 37 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:447]
2025-06-03 01:44:48,611 ERROR: 分析食谱时出错: 'Recipe' object has no attribute 'recipe_ingredients' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:545]
2025-06-03 01:44:48,611 ERROR: 错误详情: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py", line 455, in analyze_recipes
    recipe_ingredients = wr.recipe.recipe_ingredients.all()
AttributeError: 'Recipe' object has no attribute 'recipe_ingredients'
 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:546]
