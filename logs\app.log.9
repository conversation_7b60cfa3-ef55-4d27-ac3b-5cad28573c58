2025-06-02 23:18:41,212 INFO: 从副表补全recipe_id: 日期=5, 餐次=晚餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,216 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=早餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,217 INFO: 从副表补全recipe_id: 日期=6, 餐次=早餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,217 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,224 INFO: 从副表补全recipe_id: 日期=6, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,224 INFO: 发现recipe_id为空的记录: 日期=6, 餐次=晚餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,225 INFO: 从副表补全recipe_id: 日期=6, 餐次=晚餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,225 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=早餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,225 INFO: 从副表补全recipe_id: 日期=7, 餐次=早餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,226 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=午餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,226 INFO: 从副表补全recipe_id: 日期=7, 餐次=午餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,226 INFO: 发现recipe_id为空的记录: 日期=7, 餐次=晚餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,227 INFO: 从副表补全recipe_id: 日期=7, 餐次=晚餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,227 INFO: 主表数据补全完成，准备保存: 总菜品数=154, 已补全=154, 未补全=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:374]
2025-06-02 23:18:41,249 INFO: 删除现有菜单食谱(主表): weekly_menu_id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:383]
2025-06-02 23:18:41,270 INFO: 删除现有菜单食谱(副表): weekly_menu_id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:392]
2025-06-02 23:18:41,272 WARNING: 跳过无效日期: 1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-02 23:18:41,273 WARNING: 跳过无效日期: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-02 23:18:41,273 WARNING: 跳过无效日期: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-02 23:18:41,273 WARNING: 跳过无效日期: 4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-02 23:18:41,274 WARNING: 跳过无效日期: 5 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-02 23:18:41,274 WARNING: 跳过无效日期: 6 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-02 23:18:41,274 WARNING: 跳过无效日期: 7 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:401]
2025-06-02 23:18:41,464 INFO: 保存周菜单成功(主表和副表): id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:455]
2025-06-02 23:18:41,534 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:18:41,534 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:18:41,809 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:18:41,810 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:18:41,817 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:18:41,818 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:18:41,820 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:18:41,820 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:18:41,820 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:18:41,820 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:18:41,822 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:18:41,822 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:18:41,825 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:18:41,825 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:18:41,896 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:18:41,896 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:18:41,898 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:18:41,899 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:18:41,903 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:18:41,903 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:18:41,903 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:18:41,904 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:18:41,904 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
