<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消耗计划详情</title>
    <style nonce="{{ csp_nonce }}">
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* A4横向打印设置 */
        @page {
            size: A4 landscape;
            margin: 1cm 0.8cm;
        }

        html, body {
            font-family: "Microsoft YaHei", "微软雅黑", SimSun, "宋体", Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: white;
            width: 100%;
            height: 100%;
        }

        .container {
            width: 100%;
            max-width: none;
            padding: 0;
            margin: 0;
        }

        /* 页眉 */
        .header {
            text-align: center;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .title {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #000;
        }

        .subtitle {
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
        }

        /* 信息表格 */
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .info-table th,
        .info-table td {
            border: 1px solid #000;
            padding: 8px;
            vertical-align: top;
        }

        .info-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
            width: 15%;
        }

        /* 明细表格 */
        .detail-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .detail-table th,
        .detail-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: center;
            vertical-align: middle;
        }

        .detail-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 13px;
        }

        .detail-table td {
            font-size: 12px;
        }

        /* 状态标签 */
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }

        .status-planning { background-color: #ffc107; color: #000; }
        .status-approved { background-color: #17a2b8; color: white; }
        .status-executed { background-color: #28a745; color: white; }
        .status-cancelled { background-color: #dc3545; color: white; }

        .stock-sufficient { background-color: #28a745; color: white; }
        .stock-insufficient { background-color: #dc3545; color: white; }

        /* 屏幕预览样式 */
        @media screen {
            body {
                background: #f8f9fa;
                padding: 20px;
                font-size: 14px;
            }

            .container {
                background: white;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                border-radius: 8px;
                padding: 30px;
                margin: 0 auto;
                max-width: 1400px;
            }

            .action-buttons {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                display: flex;
                gap: 8px;
            }

            .action-buttons .btn {
                padding: 10px 16px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 6px;
                font-size: 13px;
                font-weight: 500;
                transition: all 0.2s ease;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .btn-primary { background: #007bff; color: white; }
            .btn-info { background: #17a2b8; color: white; }
            .btn-success { background: #28a745; color: white; }
            .btn-danger { background: #dc3545; color: white; }
            .btn-secondary { background: #6c757d; color: white; }

            .btn:hover {
                opacity: 0.9;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }

            .header {
                border-bottom: 2px solid #e9ecef;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }

            .title {
                font-size: 28px;
                color: #2c3e50;
                margin-bottom: 8px;
            }

            .subtitle {
                font-size: 16px;
                color: #6c757d;
            }

            .info-table th {
                background-color: #f8f9fa;
                color: #495057;
                font-size: 13px;
            }

            .info-table td {
                font-size: 14px;
            }

            .detail-table th {
                background-color: #e9ecef;
                color: #495057;
                font-size: 13px;
            }

            .detail-table td {
                font-size: 13px;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .action-buttons {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 20px;
                justify-content: center;
            }

            .container {
                padding: 15px;
                margin: 10px;
            }

            .info-table, .detail-table {
                font-size: 12px;
            }

            .info-table th, .info-table td,
            .detail-table th, .detail-table td {
                padding: 4px;
            }

            .title {
                font-size: 22px;
            }

            .subtitle {
                font-size: 14px;
            }
        }

        @media print {
            .no-print { display: none !important; }

            body {
                background: white;
                padding: 0;
                font-size: 12px;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
                padding: 0;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <!-- 操作按钮（仅在屏幕上显示） -->
    <div class="action-buttons no-print">
        {% if consumption_plan.status == '计划中' %}
            <button class="btn btn-info" data-onclick="approveConfirm('{{ url_for('consumption_plan.approve', id=consumption_plan.id) }}')">
                <i class="fas fa-check"></i> 审核
            </button>
            <button class="btn btn-danger" data-onclick="cancelConfirm('{{ url_for('consumption_plan.cancel', id=consumption_plan.id) }}')">
                <i class="fas fa-times"></i> 取消
            </button>
        {% elif consumption_plan.status == '已审核' %}
            <button class="btn btn-success" data-onclick="executeConfirm('{{ url_for('consumption_plan.execute', id=consumption_plan.id) }}')">
                <i class="fas fa-play"></i> 执行
            </button>
        {% endif %}
        <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-secondary">
            <i class="fas fa-list"></i> 返回列表
        </a>
    </div>

    <div class="container">
        <!-- 页眉 -->
        <div class="header">
            <div class="title">消耗计划详情</div>
            <div class="subtitle">
                计划编号：{{ consumption_plan.id }} |
                {% if consumption_plan.consumption_date %}
                    {{ consumption_plan.consumption_date.strftime('%Y年%m月%d日') }}
                {% elif menu_plan %}
                    {{ menu_plan.plan_date.strftime('%Y年%m月%d日') }}
                {% endif %}
                {% if consumption_plan.meal_type %}
                    {{ consumption_plan.meal_type }}
                {% elif menu_plan %}
                    {{ menu_plan.meal_type }}
                {% endif %}
            </div>
        </div>

        <!-- 基本信息表格 -->
        <table class="info-table">
            <tr>
                <th>消耗日期</th>
                <td>
                    {% if consumption_plan.consumption_date %}
                        {{ consumption_plan.consumption_date.strftime('%Y年%m月%d日') }}
                    {% elif menu_plan %}
                        {{ menu_plan.plan_date.strftime('%Y年%m月%d日') }}
                    {% else %}
                        -
                    {% endif %}
                </td>
                <th>餐次</th>
                <td>
                    {% if consumption_plan.meal_type %}
                        {{ consumption_plan.meal_type }}
                    {% elif menu_plan %}
                        {{ menu_plan.meal_type }}
                    {% else %}
                        -
                    {% endif %}
                </td>
                <th>用餐人数</th>
                <td>
                    {% if consumption_plan.diners_count %}
                        {{ consumption_plan.diners_count }}人
                    {% elif menu_plan and menu_plan.expected_diners %}
                        {{ menu_plan.expected_diners }}人
                    {% else %}
                        -
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>学校</th>
                <td>
                    {% if menu_plan %}
                        {{ menu_plan.area.name }}
                    {% else %}
                        {% if area_name %}
                            {{ area_name }}
                        {% else %}
                            -
                        {% endif %}
                    {% endif %}
                </td>
                <th>状态</th>
                <td>
                    {% if consumption_plan.status == '计划中' %}
                        <span class="status-badge status-planning">计划中</span>
                    {% elif consumption_plan.status == '已审核' %}
                        <span class="status-badge status-approved">已审核</span>
                    {% elif consumption_plan.status == '已执行' %}
                        <span class="status-badge status-executed">已执行</span>
                    {% elif consumption_plan.status == '已取消' %}
                        <span class="status-badge status-cancelled">已取消</span>
                    {% endif %}
                </td>
                <th>创建人</th>
                <td>{{ consumption_plan.creator.real_name or consumption_plan.creator.username }}</td>
            </tr>
            <tr>
                <th>创建时间</th>
                <td>{{ consumption_plan.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                <th>审批人</th>
                <td>{{ consumption_plan.approver.real_name or consumption_plan.approver.username if consumption_plan.approver else '-' }}</td>
                <th>备注</th>
                <td>{{ consumption_plan.notes or '-' }}</td>
            </tr>
        </table>

        <!-- 消耗明细表格 -->
        <table class="detail-table">
            <thead>
                <tr>
                    <th style="width: 20%">食材名称</th>
                    <th style="width: 12%">计划消耗量</th>
                    <th style="width: 12%">实际消耗量</th>
                    <th style="width: 8%">单位</th>
                    <th style="width: 12%">当前库存</th>
                    <th style="width: 12%">库存状态</th>
                    <th style="width: 12%">出库状态</th>
                    <th style="width: 12%">备注</th>
                </tr>
            </thead>
            <tbody>
                {% for detail in consumption_details %}
                <tr>
                    <td style="text-align: left; padding-left: 8px;">{{ detail.ingredient.name }}</td>
                    <td>{{ detail.planned_quantity }}</td>
                    <td>{{ detail.actual_quantity or '-' }}</td>
                    <td>{{ detail.unit }}</td>
                    <td>{{ inventory_status[detail.id].total if inventory_status and detail.id in inventory_status else '-' }}</td>
                    <td>
                        {% if inventory_status and detail.id in inventory_status %}
                            {% if inventory_status[detail.id].sufficient %}
                                <span class="status-badge stock-sufficient">库存充足</span>
                            {% else %}
                                <span class="status-badge stock-insufficient">库存不足</span>
                            {% endif %}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        {% if detail.status == '待出库' %}
                            <span class="status-badge status-planning">待出库</span>
                        {% elif detail.status == '已出库' %}
                            <span class="status-badge status-executed">已出库</span>
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>{{ detail.notes or '-' }}</td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="8" style="text-align: center; color: #999;">暂无消耗明细</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- 出库单信息（如果已执行） -->
        {% if consumption_plan.status == '已执行' and stock_out %}
        <table class="info-table">
            <tr>
                <th>出库单号</th>
                <td>{{ stock_out.stock_out_number }}</td>
                <th>出库日期</th>
                <td>{{ stock_out.stock_out_date.strftime('%Y-%m-%d %H:%M') if stock_out.stock_out_date else '-' }}</td>
                <th>操作人</th>
                <td>{{ stock_out.operator.real_name or stock_out.operator.username if stock_out.operator else '-' }}</td>
            </tr>
        </table>
        {% endif %}
    </div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/comprehensive-event-handler.js') }}"></script>
</body>
</html>
