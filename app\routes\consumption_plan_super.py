from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app.models import (
    ConsumptionPlan, ConsumptionDetail, Ingredient, Inventory, InventoryAlert,
    AdministrativeArea, Warehouse, IngredientCategory
)
from app import db
from datetime import datetime, date
import json

consumption_plan_super_bp = Blueprint('consumption_plan_super', __name__)

@consumption_plan_super_bp.route('/consumption-plan/super-editor')
@login_required
def super_editor():
    """消耗计划超级编辑器"""
    try:
        current_app.logger.info(f"用户 {current_user.username} (ID: {current_user.id}) 访问消费计划超级编辑器")
        current_app.logger.info(f"用户区域ID: {current_user.area_id}, 用户区域级别: {current_user.area_level}")

        # 获取当前用户所属的学校区域
        current_area = None
        try:
            current_area = current_user.get_current_area()
            current_app.logger.info(f"get_current_area() 返回: {current_area}")
        except Exception as e:
            current_app.logger.error(f"调用 get_current_area() 时出错: {str(e)}")

        if not current_area:
            # 检查用户是否有直接关联的区域
            current_app.logger.info(f"检查用户直接关联的区域: area_id={current_user.area_id}, area={current_user.area}")
            if current_user.area_id and current_user.area:
                current_area = current_user.area
                current_app.logger.info(f"使用用户直接关联的区域: {current_area.name}")
            else:
                current_app.logger.warning(f"用户 {current_user.username} 没有关联任何学校")
                flash('您没有关联到任何学校，无法使用消费计划功能。请联系管理员配置您的学校信息。', 'danger')
                return redirect(url_for('consumption_plan.index'))

        current_app.logger.info(f"最终确定的区域: {current_area.name} (ID: {current_area.id})")

        # 查找该学校的仓库（一个学校只有一个仓库）
        warehouse = Warehouse.query.filter_by(area_id=current_area.id, status='正常').first()
        current_app.logger.info(f"查找仓库结果: {warehouse}")

        if not warehouse:
            current_app.logger.warning(f"学校 {current_area.name} 没有配置仓库")
            flash(f'学校 {current_area.name} 还没有配置仓库，请联系管理员创建仓库。', 'warning')
            return redirect(url_for('consumption_plan.index'))

        current_app.logger.info(f"找到仓库: {warehouse.name} (ID: {warehouse.id})")
        current_app.logger.info("准备渲染模板")

        return render_template('consumption_plan/super_editor.html',
                              current_area=current_area,
                              warehouse=warehouse,
                              today_date=date.today().strftime('%Y-%m-%d'))

    except Exception as e:
        current_app.logger.error(f"访问消费计划超级编辑器时出错: {str(e)}")
        current_app.logger.error(f"错误类型: {type(e)}")
        import traceback
        current_app.logger.error(f"错误堆栈: {traceback.format_exc()}")
        flash('系统错误，请稍后重试', 'danger')
        return redirect(url_for('consumption_plan.index'))

@consumption_plan_super_bp.route('/consumption-plan/get-warehouses/<int:area_id>')
@login_required
def get_warehouses(area_id):
    """根据区域获取仓库列表"""
    try:
        # 检查用户是否有权限操作该区域
        if not current_user.can_access_area_by_id(area_id):
            return jsonify({'error': '您没有权限操作该区域'}), 403

        # 获取区域信息
        area = AdministrativeArea.query.get(area_id)
        if not area:
            return jsonify({
                'error': f'未找到ID为{area_id}的区域',
                'message': '请选择有效的区域'
            }), 404

        area_name = area.name

        # 获取该区域的所有仓库
        warehouses = Warehouse.query.filter_by(area_id=area_id, status='正常').all()

        # 转换为JSON格式
        warehouses_data = []
        for warehouse in warehouses:
            warehouses_data.append({
                'id': warehouse.id,
                'name': warehouse.name,
                'location': warehouse.location,
                'manager_id': warehouse.manager_id,
                'status': warehouse.status
            })

        return jsonify({
            'success': True,
            'area_id': area_id,
            'area_name': area_name,
            'warehouses': warehouses_data
        })

    except Exception as e:
        # 记录错误并返回友好的错误信息
        current_app.logger.error(f"获取仓库列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取仓库列表失败',
            'message': str(e)
        }), 500

def safe_get_int_param(param_name, default=None):
    """安全地获取整数参数，如果转换失败返回默认值"""
    try:
        value = request.args.get(param_name)
        if value is None or value == '':
            return default
        return int(value)
    except (ValueError, TypeError):
        current_app.logger.warning(f"无效的{param_name}参数: {request.args.get(param_name)}")
        return default

@consumption_plan_super_bp.route('/consumption-plan/get_inventory_batches')
@login_required
def get_inventory_batches():
    """获取仓库中的库存批次（按FIFO排序）"""
    warehouse_id = safe_get_int_param('warehouse_id')

    if not warehouse_id:
        return jsonify({'error': '缺少仓库ID参数'}), 400

    # 检查仓库是否存在
    warehouse = Warehouse.query.get(warehouse_id)
    if not warehouse:
        return jsonify({'error': '仓库不存在'}), 404

    # 检查用户是否有权限访问该仓库
    if not current_user.can_access_area_by_id(warehouse.area_id):
        return jsonify({'error': '您没有权限访问该仓库'}), 403

    try:
        # 极速优化的SQL查询 - 最小化数据传输和处理
        from sqlalchemy import text

        sql = text("""
            SELECT TOP 500
                i.id, i.ingredient_id, i.batch_number,
                CONVERT(VARCHAR(10), i.production_date, 120) as production_date,
                CONVERT(VARCHAR(10), i.expiry_date, 120) as expiry_date,
                i.quantity, i.unit, i.storage_location_id,
                ing.name as ingredient_name,
                ISNULL(cat.name, '未分类') as category_name
            FROM inventories i WITH (NOLOCK)
            INNER JOIN ingredients ing WITH (NOLOCK) ON i.ingredient_id = ing.id
            LEFT JOIN ingredient_categories cat WITH (NOLOCK) ON ing.category_id = cat.id
            WHERE i.warehouse_id = :warehouse_id
            AND i.status = '正常'
            AND TRY_CAST(i.quantity AS DECIMAL(18,2)) > 0
            ORDER BY i.id
        """)

        inventory_result = db.session.execute(sql, {
            'warehouse_id': warehouse_id
        }).fetchall()

        # 极速数据转换 - 最小化处理
        result = []
        for batch in inventory_result:
            result.append({
                'id': batch.id,
                'ingredient_id': batch.ingredient_id,
                'ingredient_name': batch.ingredient_name,
                'category': batch.category_name,
                'batch_number': batch.batch_number or '',
                'production_date': batch.production_date or '',
                'expiry_date': batch.expiry_date or '',
                'quantity': float(batch.quantity),
                'unit': batch.unit or '',
                'storage_location_id': batch.storage_location_id
            })

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"获取库存批次时出错: {str(e)}")
        return jsonify({'error': '获取库存批次时出错', 'message': str(e)}), 500

@consumption_plan_super_bp.route('/consumption-plan/create_super', methods=['POST'])
@login_required
def create_super():
    """使用超级编辑器创建消耗计划"""
    try:
        # 获取表单数据
        area_id = request.form.get('area_id', type=int)
        warehouse_id = request.form.get('warehouse_id', type=int)
        consumption_date = request.form.get('consumption_date')
        meal_types = request.form.getlist('meal_types[]')  # 获取多选餐次
        diners_count = request.form.get('diners_count', type=int)
        notes = request.form.get('notes')

        # 调试信息
        current_app.logger.info(f"超级编辑器接收到的数据:")
        current_app.logger.info(f"  area_id: {area_id}")
        current_app.logger.info(f"  warehouse_id: {warehouse_id}")
        current_app.logger.info(f"  consumption_date: {consumption_date}")
        current_app.logger.info(f"  meal_types: {meal_types}")
        current_app.logger.info(f"  diners_count: {diners_count}")
        current_app.logger.info(f"  notes: {notes}")

        # 验证必要参数
        if not all([area_id, warehouse_id, consumption_date]) or not meal_types:
            missing_fields = []
            if not area_id:
                missing_fields.append("区域")
            if not warehouse_id:
                missing_fields.append("仓库")
            if not consumption_date:
                missing_fields.append("消耗日期")
            if not meal_types:
                missing_fields.append("餐次")

            flash(f'请填写所有必要信息: {", ".join(missing_fields)}', 'danger')
            return redirect(url_for('consumption_plan_super.super_editor'))

        # 检查用户是否有权限操作该区域
        if not current_user.can_access_area_by_id(area_id):
            flash('您没有权限操作该区域', 'danger')
            return redirect('http://127.0.0.1:5000/consumption-plan')

        # 获取选中的批次
        selected_batches = request.form.getlist('selected_batches[]')

        if not selected_batches:
            flash('请至少选择一个批次进行消耗', 'warning')
            return redirect(url_for('consumption_plan_super.super_editor'))

        # 使用原始 SQL 创建消耗计划，避免 SQLAlchemy ORM 的时间戳处理问题
        from sqlalchemy import text

        # 将日期字符串转换为 date 对象
        consumption_date_obj = datetime.strptime(consumption_date, '%Y-%m-%d').date()
        formatted_date = consumption_date_obj.strftime('%Y-%m-%d')
        diners_count_value = diners_count or 1
        notes_value = (notes or '').replace("'", "''")

        created_plans = []  # 存储创建的消费计划ID

        # 为每个餐次创建单独的消费计划
        for meal_type in meal_types:
            current_app.logger.info(f"为餐次 {meal_type} 创建消费计划")

            # 查找对应的菜单计划（可选）
            menu_plan_id = None
            try:
                from app.models import MenuPlan
                menu_plan = MenuPlan.query.filter_by(
                    area_id=area_id,
                    plan_date=consumption_date_obj,
                    meal_type=meal_type
                ).first()

                if menu_plan:
                    menu_plan_id = menu_plan.id
                    current_app.logger.info(f"找到对应的菜单计划: ID={menu_plan_id}")
                else:
                    current_app.logger.info(f"未找到对应的菜单计划，将创建独立的消耗计划: area_id={area_id}, date={consumption_date_obj}, meal_type={meal_type}")
            except Exception as e:
                current_app.logger.error(f"查找菜单计划时出错: {str(e)}")
                current_app.logger.info("将创建独立的消耗计划")

            # 处理字符串中的单引号，避免 SQL 注入
            meal_type_safe = meal_type.replace("'", "''") if meal_type else ''

            sql = text(f"""
                INSERT INTO consumption_plans
                (menu_plan_id, area_id, consumption_date, meal_type, diners_count, status, created_by, notes, created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                ({menu_plan_id if menu_plan_id else 'NULL'}, {area_id}, '{formatted_date}', '{meal_type_safe}', {diners_count_value}, '计划中', {current_user.id}, '{notes_value}',
                GETDATE(), GETDATE())
            """)

            result = db.session.execute(sql)
            consumption_plan_id = result.fetchone()[0]
            created_plans.append(consumption_plan_id)

            current_app.logger.info(f"创建消费计划成功: ID={consumption_plan_id}, 餐次={meal_type}")

        # 为每个创建的消费计划添加消耗明细
        for consumption_plan_id in created_plans:
            current_app.logger.info(f"为消费计划 {consumption_plan_id} 添加消耗明细")

            for batch_id in selected_batches:
                quantity = request.form.get(f'quantity_{batch_id}', type=float)

                if quantity and quantity > 0:
                    # 获取批次信息
                    batch = Inventory.query.get(batch_id)

                    if batch:
                        # 验证消耗数量不超过库存数量
                        if quantity > batch.quantity:
                            flash(f'食材 {batch.ingredient.name} (批次号: {batch.batch_number}) 的消耗数量不能超过库存数量 {batch.quantity} {batch.unit}', 'danger')
                            db.session.rollback()
                            return redirect(url_for('consumption_plan_super.super_editor'))

                        # 使用直接构建的 SQL 语句创建消耗明细
                        # 处理字符串中的单引号，避免 SQL 注入
                        unit_safe = batch.unit.replace("'", "''") if batch.unit else ''
                        batch_number_safe = batch.batch_number.replace("'", "''") if batch.batch_number else ''

                        # 将批次信息存储在 notes 字段中
                        detail_notes_value = f'从批次 {batch_number_safe} 创建，库存ID: {batch.id}'
                        detail_notes_safe = detail_notes_value.replace("'", "''")

                        detail_sql = text(f"""
                            INSERT INTO consumption_details
                            (consumption_plan_id, ingredient_id, inventory_id, batch_number, planned_quantity, unit, status, is_main_ingredient, notes, created_at, updated_at)
                            VALUES
                            ({consumption_plan_id}, {batch.ingredient_id}, {batch.id}, '{batch_number_safe}', {quantity}, '{unit_safe}', '待出库', 1, '{detail_notes_safe}', GETDATE(), GETDATE())
                        """)

                        db.session.execute(detail_sql)

        db.session.commit()

        # 检查每个消费计划的库存是否足够
        from app.routes.consumption_plan import check_inventory
        for consumption_plan_id in created_plans:
            try:
                check_inventory(consumption_plan_id, area_id, warehouse_id)
            except Exception as e:
                current_app.logger.warning(f"检查消费计划 {consumption_plan_id} 库存时出错: {str(e)}")

        meal_types_str = "、".join(meal_types)
        flash(f'成功创建 {len(created_plans)} 个消耗计划（{meal_types_str}）', 'success')
        return redirect(url_for('consumption_plan.index'))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建消耗计划时出错: {str(e)}")
        flash(f'创建消耗计划时出错: {str(e)}', 'danger')
        return redirect(url_for('consumption_plan_super.super_editor'))

@consumption_plan_super_bp.route('/consumption-plan/analyze-recipes', methods=['POST'])
@login_required
def analyze_recipes():
    """分析食谱和食材需求"""
    try:
        data = request.get_json()
        current_app.logger.info(f"收到食谱分析请求: {data}")

        area_id = data.get('area_id')
        consumption_date = data.get('consumption_date')
        meal_types = data.get('meal_types', [])

        current_app.logger.info(f"解析参数: area_id={area_id}, consumption_date={consumption_date}, meal_types={meal_types}")

        if not all([area_id, consumption_date, meal_types]):
            current_app.logger.warning(f"缺少必要参数: area_id={area_id}, consumption_date={consumption_date}, meal_types={meal_types}")
            return jsonify({'error': '缺少必要参数'}), 400

        # 检查用户权限
        if not current_user.can_access_area_by_id(area_id):
            current_app.logger.warning(f"用户 {current_user.id} 没有权限访问区域 {area_id}")
            return jsonify({'error': '您没有权限访问该区域'}), 403

        from sqlalchemy import text
        from datetime import datetime

        # 转换日期格式
        consumption_date_obj = datetime.strptime(consumption_date, '%Y-%m-%d').date()

        current_app.logger.info(f"查询日期: {consumption_date_obj}, 区域: {area_id}, 餐次: {meal_types}")

        # 优化：使用单个SQL查询获取所有食谱信息
        from sqlalchemy import text

        # 计算星期几
        weekday = consumption_date_obj.weekday() + 2
        if weekday > 7:
            weekday = 1

        # 简化查询，分别查询菜单计划和周菜单
        recipe_results = []

        # 查询菜单计划中的食谱（通过menu_recipes关联表）
        menu_plan_sql = text("""
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners
            FROM menu_plans mp
            JOIN menu_recipes mr ON mp.id = mr.menu_plan_id
            JOIN recipes r ON mr.recipe_id = r.id
            WHERE mp.area_id = :area_id
            AND mp.plan_date = :plan_date
        """)

        menu_plan_results = db.session.execute(menu_plan_sql, {
            'area_id': area_id,
            'plan_date': consumption_date_obj
        }).fetchall()

        # 过滤餐次
        for result in menu_plan_results:
            if result.meal_type in meal_types:
                recipe_results.append(result)

        # 如果菜单计划中没有找到，查询周菜单
        if not recipe_results:
            weekly_menu_sql = text("""
                SELECT r.id, r.name as recipe_name, wmr.meal_type,
                       COALESCE(wm.expected_diners, 0) as expected_diners
                FROM weekly_menus wm
                JOIN weekly_menu_recipes wmr ON wm.id = wmr.weekly_menu_id
                JOIN recipes r ON wmr.recipe_id = r.id
                WHERE wm.area_id = :area_id
                AND wm.week_start <= :plan_date
                AND wm.week_end >= :plan_date
                AND wmr.day_of_week = :weekday
            """)

            weekly_results = db.session.execute(weekly_menu_sql, {
                'area_id': area_id,
                'plan_date': consumption_date_obj,
                'weekday': weekday
            }).fetchall()

            # 过滤餐次
            for result in weekly_results:
                if result.meal_type in meal_types:
                    recipe_results.append(result)

        current_app.logger.info(f"找到 {len(recipe_results)} 个食谱")

        recipes = []

        # 如果找到食谱，批量获取食材信息
        if recipe_results:
            # 收集所有食谱ID
            recipe_ids = [r.id for r in recipe_results]

            # 批量查询所有食谱的食材信息

            # 使用ORM查询避免IN子句的参数绑定问题
            from app.models import RecipeIngredient, Ingredient, IngredientCategory

            recipe_ingredients_query = db.session.query(
                RecipeIngredient.recipe_id,
                RecipeIngredient.ingredient_id,
                RecipeIngredient.quantity,
                RecipeIngredient.unit,
                Ingredient.name.label('ingredient_name'),
                Ingredient.is_condiment,
                IngredientCategory.name.label('category_name')
            ).join(
                Ingredient, RecipeIngredient.ingredient_id == Ingredient.id
            ).outerjoin(
                IngredientCategory, Ingredient.category_id == IngredientCategory.id
            ).filter(
                RecipeIngredient.recipe_id.in_(recipe_ids)
            ).filter(
                db.or_(
                    Ingredient.is_condiment == 0,
                    Ingredient.is_condiment.is_(None)
                )
            ).filter(
                db.or_(
                    IngredientCategory.name.is_(None),
                    ~IngredientCategory.name.like('%调味料%'),
                    ~IngredientCategory.name.like('%调料%'),
                    ~IngredientCategory.name.like('%香料%')
                )
            )

            recipe_ingredients = recipe_ingredients_query.all()

            # 按食谱ID组织食材数据
            ingredients_by_recipe = {}
            for ri in recipe_ingredients:
                if ri.recipe_id not in ingredients_by_recipe:
                    ingredients_by_recipe[ri.recipe_id] = []
                ingredients_by_recipe[ri.recipe_id].append(ri)

            # 处理每个食谱
            for recipe_row in recipe_results:
                current_app.logger.info(f"分析食谱: {recipe_row.recipe_name}")

                main_ingredients = []
                recipe_ingredients_list = ingredients_by_recipe.get(recipe_row.id, [])

                for ri in recipe_ingredients_list:
                    main_ingredients.append({
                        'name': ri.ingredient_name,
                        'required_quantity': float(ri.quantity or 0),
                        'unit': ri.unit or '',
                        'ingredient_id': ri.ingredient_id
                    })

                recipes.append({
                    'recipe_id': recipe_row.id,
                    'recipe_name': recipe_row.recipe_name,
                    'meal_type': recipe_row.meal_type,
                    'expected_diners': recipe_row.expected_diners,
                    'main_ingredients': main_ingredients
                })
        else:
            # 没有找到食谱
            current_app.logger.info("暂无食谱安排")
            for meal_type in meal_types:
                recipes.append({
                    'recipe_id': None,
                    'recipe_name': '暂无安排',
                    'meal_type': meal_type,
                    'expected_diners': None,
                    'main_ingredients': []
                })

        return jsonify({
            'recipes': recipes,
            'missing_ingredients': [],
            'total_missing_count': 0
        })

    except Exception as e:
        current_app.logger.error(f"分析食谱时出错: {str(e)}")
        return jsonify({'error': '分析食谱时出错', 'message': str(e)}), 500





