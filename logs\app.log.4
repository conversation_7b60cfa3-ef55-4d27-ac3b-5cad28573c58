2025-06-02 23:18:41,191 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=晚餐, 菜品=🏫 缤纷吐司蒸 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,196 INFO: 从副表补全recipe_id: 日期=3, 餐次=晚餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,196 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=晚餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,197 INFO: 从副表补全recipe_id: 日期=3, 餐次=晚餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,198 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=晚餐, 菜品=🏫 西瓜桃子面 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,198 INFO: 从副表补全recipe_id: 日期=3, 餐次=晚餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,198 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=晚餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,199 INFO: 从副表补全recipe_id: 日期=3, 餐次=晚餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,199 INFO: 发现recipe_id为空的记录: 日期=3, 餐次=晚餐, 菜品=🏫 鲜椒青瓜干 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,200 INFO: 从副表补全recipe_id: 日期=3, 餐次=晚餐, 菜品=🏫 鲜椒青瓜干, ID=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,200 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=早餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,200 INFO: 从副表补全recipe_id: 日期=4, 餐次=早餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,200 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=早餐, 菜品=🏫 鲜笋烧仔排 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,201 INFO: 从副表补全recipe_id: 日期=4, 餐次=早餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,201 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=早餐, 菜品=🏫 缤纷吐司蒸 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,201 INFO: 从副表补全recipe_id: 日期=4, 餐次=早餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,202 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=早餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,202 INFO: 从副表补全recipe_id: 日期=4, 餐次=早餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,202 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=早餐, 菜品=🏫 西瓜桃子面 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,202 INFO: 从副表补全recipe_id: 日期=4, 餐次=早餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,203 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=早餐, 菜品=🏫 鲜椒青瓜干 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,203 INFO: 从副表补全recipe_id: 日期=4, 餐次=早餐, 菜品=🏫 鲜椒青瓜干, ID=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,203 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=早餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,204 INFO: 从副表补全recipe_id: 日期=4, 餐次=早餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,204 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=午餐, 菜品=🏫 鲜椒青瓜干 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,204 INFO: 从副表补全recipe_id: 日期=4, 餐次=午餐, 菜品=🏫 鲜椒青瓜干, ID=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,204 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=午餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,205 INFO: 从副表补全recipe_id: 日期=4, 餐次=午餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,205 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=午餐, 菜品=🏫 黑木耳炒山药 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,205 INFO: 从副表补全recipe_id: 日期=4, 餐次=午餐, 菜品=🏫 黑木耳炒山药, ID=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,206 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=午餐, 菜品=🏫 鲜笋烧仔排 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,206 INFO: 从副表补全recipe_id: 日期=4, 餐次=午餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,206 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,206 INFO: 从副表补全recipe_id: 日期=4, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,207 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=午餐, 菜品=🏫 缤纷吐司蒸 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,207 INFO: 从副表补全recipe_id: 日期=4, 餐次=午餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,207 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=晚餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,208 INFO: 从副表补全recipe_id: 日期=4, 餐次=晚餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,208 INFO: 发现recipe_id为空的记录: 日期=4, 餐次=晚餐, 菜品=🏫 西瓜桃子面 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,208 INFO: 从副表补全recipe_id: 日期=4, 餐次=晚餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,209 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=早餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,210 INFO: 从副表补全recipe_id: 日期=5, 餐次=早餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,211 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=午餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,211 INFO: 从副表补全recipe_id: 日期=5, 餐次=午餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,211 INFO: 发现recipe_id为空的记录: 日期=5, 餐次=晚餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
