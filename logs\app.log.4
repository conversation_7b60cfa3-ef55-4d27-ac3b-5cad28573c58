2025-06-02 23:31:33,458 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:31:33,463 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:31:33,472 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:31:33,472 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:31:33,472 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:31:33,473 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:31:33,547 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:31:33,548 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:31:33,579 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:31:33,579 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:31:33,641 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:31:33,641 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:32:03,567 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:32:03,567 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:32:17,824 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:32:17,824 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:32:18,337 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:32:18,337 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:32:18,483 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:32:18,483 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:32:18,736 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:32:18,736 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:32:18,737 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:32:18,737 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:32:18,820 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:32:18,820 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:32:21,935 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:32:21,935 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:32:21,979 INFO: 使用消耗计划的区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-02 23:32:21,979 INFO: 通过消耗计划信息读取菜谱：日期=2025-05-29, 餐次=早餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-02 23:32:21,979 INFO: 查询周菜单：日期=2025-05-29, 星期=3(0=周一), day_of_week=4, 餐次=早餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:242]
2025-06-02 23:32:21,995 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:252]
2025-06-02 23:32:21,995 INFO:   - 周菜单ID: 36, 开始日期: 2025-05-26, 结束日期: 2025-06-01, 状态: 已发布 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:254]
2025-06-02 23:32:22,001 INFO: 周菜单 36 总共有 61 条食谱记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:260]
2025-06-02 23:32:22,001 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-02 23:32:22,002 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-02 23:32:22,002 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-02 23:32:22,002 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-02 23:32:22,002 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=56 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-02 23:32:22,003 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=110 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-02 23:32:22,003 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=116 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-02 23:32:22,003 INFO:   - 食谱记录: day_of_week=3, meal_type='早餐', recipe_id=65 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-02 23:32:22,003 INFO:   - 食谱记录: day_of_week=3, meal_type='午餐', recipe_id=2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-02 23:32:22,004 INFO:   - 食谱记录: day_of_week=3, meal_type='午餐', recipe_id=26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-02 23:32:22,004 INFO:   - 食谱记录: day_of_week=3, meal_type='午餐', recipe_id=56 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-02 23:32:22,004 INFO:   - 食谱记录: day_of_week=3, meal_type='午餐', recipe_id=110 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
2025-06-02 23:32:22,004 INFO:   - 食谱记录: day_of_week=3, meal_type='午餐', recipe_id=116 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:263]
