2025-06-03 00:54:17,923 ERROR: 分析食谱时出错: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            -- 从菜单计划获取食谱
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners, 'menu_plan' as source
            FROM menu_plans mp
            JOIN recipes r ON mp.recipe_id = r.id
            WHERE mp.area_id = ?
            AND mp.plan_date = ?
            AND mp.meal_type IN ('午餐')

            UNION ALL

            -- 从周菜单获取食谱
            SELECT r.id, r.name as recipe_name, wmr.meal_type,
                   COALESCE(wm.expected_diners, 0) as expected_diners, 'weekly_menu' as source
            FROM weekly_menus wm
            JOIN weekly_menu_recipes wmr ON wm.id = wmr.weekly_menu_id
            JOIN recipes r ON wmr.recipe_id = r.id
            WHERE wm.area_id = ?
            AND wm.week_start <= ?
            AND wm.week_end >= ?
            AND wmr.day_of_week = ?
            AND wmr.meal_type IN ('午餐')
        ]
[parameters: (42, datetime.date(2025, 6, 3), 42, datetime.date(2025, 6, 3), datetime.date(2025, 6, 3), 3)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:515]
2025-06-03 00:54:42,651 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:371]
2025-06-03 00:54:42,652 INFO: 解析参数: area_id=42, consumption_date=2025-06-03, meal_types=['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:377]
2025-06-03 00:54:42,654 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:394]
2025-06-03 00:54:42,654 ERROR: 分析食谱时出错: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            -- 从菜单计划获取食谱
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners, 'menu_plan' as source
            FROM menu_plans mp
            JOIN recipes r ON mp.recipe_id = r.id
            WHERE mp.area_id = ?
            AND mp.plan_date = ?
            AND mp.meal_type IN ('早餐')

            UNION ALL

            -- 从周菜单获取食谱
            SELECT r.id, r.name as recipe_name, wmr.meal_type,
                   COALESCE(wm.expected_diners, 0) as expected_diners, 'weekly_menu' as source
            FROM weekly_menus wm
            JOIN weekly_menu_recipes wmr ON wm.id = wmr.weekly_menu_id
            JOIN recipes r ON wmr.recipe_id = r.id
            WHERE wm.area_id = ?
            AND wm.week_start <= ?
            AND wm.week_end >= ?
            AND wmr.day_of_week = ?
            AND wmr.meal_type IN ('早餐')
        ]
[parameters: (42, datetime.date(2025, 6, 3), 42, datetime.date(2025, 6, 3), datetime.date(2025, 6, 3), 3)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:515]
2025-06-03 00:54:44,784 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:371]
2025-06-03 00:54:44,785 INFO: 解析参数: area_id=42, consumption_date=2025-06-03, meal_types=['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:377]
2025-06-03 00:54:44,787 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:394]
2025-06-03 00:54:44,787 ERROR: 分析食谱时出错: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            -- 从菜单计划获取食谱
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners, 'menu_plan' as source
            FROM menu_plans mp
            JOIN recipes r ON mp.recipe_id = r.id
            WHERE mp.area_id = ?
            AND mp.plan_date = ?
            AND mp.meal_type IN ('早餐')

            UNION ALL

            -- 从周菜单获取食谱
            SELECT r.id, r.name as recipe_name, wmr.meal_type,
                   COALESCE(wm.expected_diners, 0) as expected_diners, 'weekly_menu' as source
            FROM weekly_menus wm
            JOIN weekly_menu_recipes wmr ON wm.id = wmr.weekly_menu_id
            JOIN recipes r ON wmr.recipe_id = r.id
            WHERE wm.area_id = ?
            AND wm.week_start <= ?
            AND wm.week_end >= ?
            AND wmr.day_of_week = ?
            AND wmr.meal_type IN ('早餐')
        ]
[parameters: (42, datetime.date(2025, 6, 3), 42, datetime.date(2025, 6, 3), datetime.date(2025, 6, 3), 3)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:515]
2025-06-03 00:54:47,105 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['午餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:371]
2025-06-03 00:54:47,107 INFO: 解析参数: area_id=42, consumption_date=2025-06-03, meal_types=['午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:377]
2025-06-03 00:54:47,109 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:394]
2025-06-03 00:54:47,109 ERROR: 分析食谱时出错: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            -- 从菜单计划获取食谱
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners, 'menu_plan' as source
            FROM menu_plans mp
            JOIN recipes r ON mp.recipe_id = r.id
            WHERE mp.area_id = ?
            AND mp.plan_date = ?
            AND mp.meal_type IN ('午餐')

            UNION ALL

            -- 从周菜单获取食谱
            SELECT r.id, r.name as recipe_name, wmr.meal_type,
                   COALESCE(wm.expected_diners, 0) as expected_diners, 'weekly_menu' as source
            FROM weekly_menus wm
            JOIN weekly_menu_recipes wmr ON wm.id = wmr.weekly_menu_id
            JOIN recipes r ON wmr.recipe_id = r.id
            WHERE wm.area_id = ?
            AND wm.week_start <= ?
            AND wm.week_end >= ?
            AND wmr.day_of_week = ?
            AND wmr.meal_type IN ('午餐')
        ]
[parameters: (42, datetime.date(2025, 6, 3), 42, datetime.date(2025, 6, 3), datetime.date(2025, 6, 3), 3)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:515]
2025-06-03 00:54:52,542 INFO: 用户 18373062333 (ID: 34) 访问消费计划超级编辑器 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:18]
2025-06-03 00:54:52,542 INFO: 用户区域ID: 42, 用户区域级别: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:19]
2025-06-03 00:54:52,544 INFO: get_current_area() 返回: <AdministrativeArea 42> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:25]
2025-06-03 00:54:52,544 INFO: 最终确定的区域: 朝阳区实验中学 (ID: 42) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:40]
2025-06-03 00:54:52,550 INFO: 查找仓库结果: <Warehouse 4> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:44]
2025-06-03 00:54:52,550 INFO: 找到仓库: 朝阳区实验中学中心仓库 (ID: 4) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:51]
2025-06-03 00:54:52,551 INFO: 准备渲染模板 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:52]
2025-06-03 00:55:57,534 INFO: 用户 18373062333 (ID: 34) 访问消费计划超级编辑器 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:18]
2025-06-03 00:55:57,534 INFO: 用户区域ID: 42, 用户区域级别: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:19]
2025-06-03 00:55:57,536 INFO: get_current_area() 返回: <AdministrativeArea 42> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:25]
2025-06-03 00:55:57,536 INFO: 最终确定的区域: 朝阳区实验中学 (ID: 42) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:40]
2025-06-03 00:55:57,584 INFO: 查找仓库结果: <Warehouse 4> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:44]
2025-06-03 00:55:57,584 INFO: 找到仓库: 朝阳区实验中学中心仓库 (ID: 4) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:51]
2025-06-03 00:55:57,585 INFO: 准备渲染模板 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:52]
