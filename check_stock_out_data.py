#!/usr/bin/env python3
"""
检查出库单数据
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import StockOut

def check_stock_out_data():
    """检查出库单数据"""
    app = create_app()
    
    with app.app_context():
        print("=== 检查出库单数据 ===")
        
        # 查询所有出库单
        stock_outs = StockOut.query.all()
        print(f"总共有 {len(stock_outs)} 个出库单")
        
        for stock_out in stock_outs:
            print(f"\n出库单: {stock_out.stock_out_number}")
            print(f"  ID: {stock_out.id} (类型: {type(stock_out.id)})")
            print(f"  状态: {stock_out.status}")
            print(f"  仓库: {stock_out.warehouse.name if stock_out.warehouse else 'None'}")
            
            if stock_out.id is None:
                print(f"  ❌ ID为None!")
            elif stock_out.id == 0:
                print(f"  ⚠️ ID为0!")
            else:
                print(f"  ✅ ID正常: {stock_out.id}")

if __name__ == '__main__':
    check_stock_out_data()
