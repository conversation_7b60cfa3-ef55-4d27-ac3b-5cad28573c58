2025-06-03 01:15:51,227 ERROR: 分析食谱时出错: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners
            FROM menu_plans mp
            JOIN menu_recipes mr ON mp.id = mr.menu_plan_id
            JOIN recipes r ON mr.recipe_id = r.id
            WHERE mp.area_id = ?
            AND mp.plan_date = ?
        ]
[parameters: (42, datetime.date(2025, 6, 3))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:508]
2025-06-03 01:17:32,716 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 01:17:46,817 INFO: 用户 18373062333 (ID: 34) 访问消费计划超级编辑器 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:18]
2025-06-03 01:17:46,818 INFO: 用户区域ID: 42, 用户区域级别: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:19]
2025-06-03 01:17:46,825 INFO: get_current_area() 返回: <AdministrativeArea 42> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:25]
2025-06-03 01:17:46,825 INFO: 最终确定的区域: 朝阳区实验中学 (ID: 42) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:40]
2025-06-03 01:17:46,846 INFO: 查找仓库结果: <Warehouse 4> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:44]
2025-06-03 01:17:46,847 INFO: 找到仓库: 朝阳区实验中学中心仓库 (ID: 4) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:51]
2025-06-03 01:17:46,847 INFO: 准备渲染模板 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:52]
2025-06-03 01:17:51,467 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:355]
2025-06-03 01:17:51,467 INFO: 解析参数: area_id=42, consumption_date=2025-06-03, meal_types=['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:17:51,471 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:378]
2025-06-03 01:17:51,472 ERROR: 分析食谱时出错: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners
            FROM menu_plans mp
            JOIN menu_recipes mr ON mp.id = mr.menu_plan_id
            JOIN recipes r ON mr.recipe_id = r.id
            WHERE mp.area_id = ?
            AND mp.plan_date = ?
        ]
[parameters: (42, datetime.date(2025, 6, 3))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:513]
2025-06-03 01:17:53,108 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:355]
2025-06-03 01:17:53,108 INFO: 解析参数: area_id=42, consumption_date=2025-06-03, meal_types=['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:17:53,112 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:378]
2025-06-03 01:17:53,112 ERROR: 分析食谱时出错: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners
            FROM menu_plans mp
            JOIN menu_recipes mr ON mp.id = mr.menu_plan_id
            JOIN recipes r ON mr.recipe_id = r.id
            WHERE mp.area_id = ?
            AND mp.plan_date = ?
        ]
[parameters: (42, datetime.date(2025, 6, 3))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:513]
2025-06-03 01:17:56,467 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:355]
2025-06-03 01:17:56,468 INFO: 解析参数: area_id=42, consumption_date=2025-06-03, meal_types=['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:17:56,470 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:378]
2025-06-03 01:17:56,471 ERROR: 分析食谱时出错: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners
            FROM menu_plans mp
            JOIN menu_recipes mr ON mp.id = mr.menu_plan_id
            JOIN recipes r ON mr.recipe_id = r.id
            WHERE mp.area_id = ?
            AND mp.plan_date = ?
        ]
[parameters: (42, datetime.date(2025, 6, 3))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:513]
2025-06-03 01:17:57,695 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:355]
2025-06-03 01:17:57,695 INFO: 解析参数: area_id=42, consumption_date=2025-06-03, meal_types=['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:17:57,695 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:378]
2025-06-03 01:17:57,704 ERROR: 分析食谱时出错: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners
            FROM menu_plans mp
            JOIN menu_recipes mr ON mp.id = mr.menu_plan_id
            JOIN recipes r ON mr.recipe_id = r.id
            WHERE mp.area_id = ?
            AND mp.plan_date = ?
        ]
[parameters: (42, datetime.date(2025, 6, 3))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:513]
2025-06-03 01:18:24,595 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 01:18:40,140 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 01:19:56,649 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 01:20:51,213 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-03 01:21:57,243 INFO: 用户 18373062333 (ID: 34) 访问消费计划超级编辑器 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:18]
2025-06-03 01:21:57,243 INFO: 用户区域ID: 42, 用户区域级别: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:19]
2025-06-03 01:21:57,250 INFO: get_current_area() 返回: <AdministrativeArea 42> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:25]
2025-06-03 01:21:57,250 INFO: 最终确定的区域: 朝阳区实验中学 (ID: 42) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:40]
2025-06-03 01:21:57,256 INFO: 查找仓库结果: <Warehouse 4> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:44]
2025-06-03 01:21:57,256 INFO: 找到仓库: 朝阳区实验中学中心仓库 (ID: 4) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:51]
2025-06-03 01:21:57,256 INFO: 准备渲染模板 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:52]
2025-06-03 01:22:02,942 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:355]
2025-06-03 01:22:02,943 INFO: 解析参数: area_id=42, consumption_date=2025-06-03, meal_types=['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:22:02,949 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:378]
2025-06-03 01:24:43,700 INFO: 准备渲染模板 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:52]
