2025-06-03 00:29:05,154 INFO: 分析食谱: 黄米南瓜盅（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:469]
2025-06-03 00:29:05,168 INFO: 食谱 黄米南瓜盅（朝阳区实验中学版） 包含 6 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:496]
2025-06-03 00:29:05,169 INFO: 检查食材: 食用油 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:05,169 INFO: 食材 食用油: 需要 20.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:05,170 INFO: 检查食材: 盐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:05,170 INFO: 食材 盐: 需要 5.0g, 库存 0.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:05,171 INFO: 检查食材: 生抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:05,171 INFO: 食材 生抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:05,171 INFO: 检查食材: 料酒 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:05,172 INFO: 食材 料酒: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:05,172 INFO: 检查食材: 老抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:05,173 INFO: 食材 老抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:05,173 INFO: 检查食材: 南瓜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:05,174 INFO: 食材 南瓜: 需要 100.0g, 库存 200.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:07,426 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-03 00:29:07,427 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-03 00:29:07,427 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['午餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:383]
2025-06-03 00:29:07,427 INFO: 解析参数: area_id=42, consumption_date=2025-06-03, meal_types=['午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:389]
2025-06-03 00:29:07,428 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:406]
2025-06-03 00:29:07,429 INFO: 查询餐次: 午餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:413]
2025-06-03 00:29:07,436 INFO: 找到 2 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-03 00:29:07,437 INFO: 分析食谱: 米饭（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:469]
2025-06-03 00:29:07,442 INFO: 食谱 米饭（朝阳区实验中学版） 包含 1 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:496]
2025-06-03 00:29:07,442 INFO: 检查食材: 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:07,444 INFO: 食材 米饭: 需要 1.0克, 库存 699.8克 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:07,445 INFO: 分析食谱: 西瓜桃子面（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:469]
2025-06-03 00:29:07,451 INFO: 食谱 西瓜桃子面（朝阳区实验中学版） 包含 8 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:496]
2025-06-03 00:29:07,452 INFO: 检查食材: 猪肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:07,452 INFO: 食材 猪肉: 需要 1.0千克, 库存 0.0千克 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:07,453 INFO: 检查食材: 牛肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:07,453 INFO: 食材 牛肉: 需要 1.0千克, 库存 0.0千克 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:07,453 INFO: 检查食材: 鸡肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:07,454 INFO: 食材 鸡肉: 需要 1.0千克, 库存 3000.0千克 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:07,454 INFO: 检查食材: 白菜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:07,455 INFO: 食材 白菜: 需要 1.0千克, 库存 0.0千克 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:07,455 INFO: 检查食材: 胡萝卜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:07,456 INFO: 食材 胡萝卜: 需要 1.0千克, 库存 377.0千克 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:07,456 INFO: 检查食材: 番茄酱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:07,457 INFO: 食材 番茄酱: 需要 162.0克, 库存 0.0克 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:07,457 INFO: 检查食材: 西瓜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:07,458 INFO: 食材 西瓜: 需要 109.0克, 库存 0.0克 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:07,458 INFO: 检查食材: 桃子 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:504]
2025-06-03 00:29:07,458 INFO: 食材 桃子: 需要 1.0个, 库存 0.0个 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:530]
2025-06-03 00:29:09,873 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-03 00:29:09,874 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-03 00:29:09,874 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-03 00:29:09,874 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-03 00:29:09,876 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-03 00:29:09,876 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-03 00:29:12,057 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-03 00:29:12,057 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-03 00:29:12,057 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-03 00:29:12,057 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-03 00:29:12,100 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
