2025-06-02 23:18:41,136 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,141 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,141 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 黑木耳炒山药 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,142 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 黑木耳炒山药, ID=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,142 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 缤纷吐司蒸 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,143 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,143 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=早餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,143 INFO: 从副表补全recipe_id: 日期=1, 餐次=早餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,144 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 西瓜桃子面 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,144 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,145 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 鲜笋烧仔排 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,145 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,145 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,146 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,146 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,147 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,147 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 鲜椒青瓜干 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,148 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 鲜椒青瓜干, ID=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,148 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 黑木耳炒山药 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,149 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 黑木耳炒山药, ID=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,149 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=午餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,149 INFO: 从副表补全recipe_id: 日期=1, 餐次=午餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,153 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,154 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,154 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 西瓜桃子面 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,154 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,155 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,155 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,155 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,156 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,156 INFO: 发现recipe_id为空的记录: 日期=1, 餐次=晚餐, 菜品=🏫 缤纷吐司蒸 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,157 INFO: 从副表补全recipe_id: 日期=1, 餐次=晚餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,157 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 鲜椒青瓜干 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,157 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 鲜椒青瓜干, ID=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,158 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,158 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,158 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 黑木耳炒山药 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,159 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 黑木耳炒山药, ID=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,159 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 鲜笋烧仔排 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,159 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,160 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,160 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,160 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=早餐, 菜品=🏫 缤纷吐司蒸 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
2025-06-02 23:18:41,161 INFO: 从副表补全recipe_id: 日期=2, 餐次=早餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:356]
2025-06-02 23:18:41,161 INFO: 发现recipe_id为空的记录: 日期=2, 餐次=午餐, 菜品=🏫 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:344]
