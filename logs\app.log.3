2025-06-02 23:32:22,050 INFO: 食谱 开胃玉米炸 的主要食材: ['酸奶', '南瓜', '玉米'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:326]
2025-06-02 23:32:22,060 INFO: 食谱 豌豆莴笋烩 的主要食材: ['豌豆', '莴笋'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:326]
2025-06-02 23:32:22,064 INFO: 步骤1: 读取消耗日期: 2025-05-29, 餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-02 23:32:22,065 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:73]
2025-06-02 23:32:22,066 INFO: 步骤2: 为出库食材 '五花肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,067 INFO: 步骤2: 为出库食材 '淀粉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,068 INFO: 步骤2: 为出库食材 '花生米' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,069 INFO: 步骤2: 为出库食材 '香肠' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,070 INFO: 步骤2: 为出库食材 '鸡肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,071 INFO: 步骤2: 为出库食材 '牛排' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,072 INFO: 步骤2: 为出库食材 '鸡胸肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,073 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,073 INFO: 步骤2: 为出库食材 '五花肉' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,074 INFO: 步骤2: 为出库食材 '鸡翅' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,074 INFO: 步骤2: 为出库食材 '葱' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,075 INFO: 步骤2: 为出库食材 '洋葱' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,077 INFO: 步骤2: 为出库食材 '西兰花' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,078 INFO: 步骤2: 为出库食材 '玉米粒' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,079 INFO: 步骤2: 为出库食材 '南瓜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,079 INFO: 步骤2: 为出库食材 '莴笋' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,080 INFO: 步骤2: 为出库食材 '彩椒' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,081 INFO: 步骤2: 为出库食材 '芹菜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,158 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,158 INFO: 步骤2: 为出库食材 '豌豆' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,160 INFO: 步骤2: 为出库食材 '茄子' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,161 INFO: 步骤2: 为出库食材 '大米' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 23:32:22,162 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:137]
2025-06-02 23:32:22,193 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,200 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,202 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,205 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,207 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,211 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,214 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,217 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,220 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,221 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,222 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,224 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,244 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,261 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,263 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,264 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,264 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,265 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,266 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,267 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,268 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 23:32:22,986 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:32:22,986 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:32:23,148 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 23:32:23,149 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 23:32:23,324 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
