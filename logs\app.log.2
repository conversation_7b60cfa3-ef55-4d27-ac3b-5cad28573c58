2025-06-03 01:42:47,458 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:352]
2025-06-03 01:42:47,600 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:42:47,603 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 01:42:47,603 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:384]
2025-06-03 01:42:47,604 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:390]
2025-06-03 01:42:47,607 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:399]
2025-06-03 01:42:47,610 INFO: 周菜单 37 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:410]
2025-06-03 01:42:47,610 INFO: 查询餐次: 午餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:352]
2025-06-03 01:42:47,612 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:42:47,613 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 01:42:47,613 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:384]
2025-06-03 01:42:47,613 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:390]
2025-06-03 01:42:47,615 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:399]
2025-06-03 01:42:47,616 INFO: 周菜单 37 中找到 7 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:410]
2025-06-03 01:42:47,616 INFO: 查询餐次: 晚餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:352]
2025-06-03 01:42:47,618 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:42:47,619 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 01:42:47,620 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:384]
2025-06-03 01:42:47,620 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:390]
2025-06-03 01:42:47,632 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:399]
2025-06-03 01:42:47,641 INFO: 周菜单 37 中找到 5 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:410]
2025-06-03 01:43:35,094 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐', '午餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:329]
2025-06-03 01:43:35,099 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐', '午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:347]
2025-06-03 01:43:35,099 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:352]
2025-06-03 01:43:35,247 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:43:35,247 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 01:43:35,247 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:384]
2025-06-03 01:43:35,247 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:390]
2025-06-03 01:43:35,262 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:399]
2025-06-03 01:43:35,266 INFO: 周菜单 37 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:410]
2025-06-03 01:43:35,266 INFO: 查询餐次: 午餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:352]
2025-06-03 01:43:35,268 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:43:35,268 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 01:43:35,269 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:384]
2025-06-03 01:43:35,269 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:390]
2025-06-03 01:43:35,270 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:399]
2025-06-03 01:43:35,271 INFO: 周菜单 37 中找到 7 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:410]
2025-06-03 01:43:35,655 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:329]
2025-06-03 01:43:35,656 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:347]
2025-06-03 01:43:35,657 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:352]
2025-06-03 01:43:35,658 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:43:35,659 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:365]
2025-06-03 01:43:35,659 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:384]
2025-06-03 01:43:35,659 INFO: 星期几: 2 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:390]
2025-06-03 01:43:35,660 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:399]
2025-06-03 01:43:35,661 INFO: 周菜单 37 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:410]
2025-06-03 01:43:49,331 INFO: 超级编辑器接收到的数据: [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:202]
2025-06-03 01:43:49,331 INFO:   area_id: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:203]
2025-06-03 01:43:49,331 INFO:   warehouse_id: 4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:204]
2025-06-03 01:43:49,332 INFO:   consumption_date: 2025-06-03 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:205]
2025-06-03 01:43:49,332 INFO:   meal_type: None [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:206]
2025-06-03 01:43:49,332 INFO:   diners_count: 1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:207]
2025-06-03 01:43:49,332 INFO:   notes:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:208]
2025-06-03 01:43:49,332 INFO: 超级编辑器保存数据: area_id=42, warehouse_id=4, consumption_date=2025-06-03, meal_type=None [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:211]
2025-06-03 01:43:59,148 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:329]
2025-06-03 01:43:59,153 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:347]
2025-06-03 01:43:59,153 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:352]
2025-06-03 01:43:59,177 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
