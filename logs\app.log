2025-06-03 00:41:55,917 ERROR: 撤销入库操作时出错: (pyodbc.IntegrityError) ('23000', '[23000] [Microsoft][ODBC SQL Server Driver][SQL Server]DELETE 语句与 REFERENCE 约束"FK_consumption_details_inventories"冲突。该冲突发生于数据库"StudentsCMSSP"，表"dbo.consumption_details", column \'inventory_id\'。 (547) (SQLExecDirectW); [23000] [Microsoft][ODBC SQL Server Driver][SQL Server]语句已终止。 (3621)')
[SQL: 
                        DELETE FROM inventories WHERE id = ?
                        ]
[parameters: (63,)]
(Background on this error at: https://sqlalche.me/e/20/gkpj) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_in.py:692]
