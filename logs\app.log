2025-06-03 00:54:17,923 ERROR: 分析食谱时出错: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            -- 从菜单计划获取食谱
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners, 'menu_plan' as source
            FROM menu_plans mp
            JOIN recipes r ON mp.recipe_id = r.id
            WHERE mp.area_id = ?
            AND mp.plan_date = ?
            AND mp.meal_type IN ('午餐')

            UNION ALL

            -- 从周菜单获取食谱
            SELECT r.id, r.name as recipe_name, wmr.meal_type,
                   COALESCE(wm.expected_diners, 0) as expected_diners, 'weekly_menu' as source
            FROM weekly_menus wm
            JOIN weekly_menu_recipes wmr ON wm.id = wmr.weekly_menu_id
            JOIN recipes r ON wmr.recipe_id = r.id
            WHERE wm.area_id = ?
            AND wm.week_start <= ?
            AND wm.week_end >= ?
            AND wmr.day_of_week = ?
            AND wmr.meal_type IN ('午餐')
        ]
[parameters: (42, datetime.date(2025, 6, 3), 42, datetime.date(2025, 6, 3), datetime.date(2025, 6, 3), 3)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:515]
