2025-06-03 01:11:15,179 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-03', 'meal_types': ['晚餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:355]
2025-06-03 01:11:15,189 INFO: 解析参数: area_id=42, consumption_date=2025-06-03, meal_types=['晚餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-03 01:11:15,198 INFO: 查询日期: 2025-06-03, 区域: 42, 餐次: ['晚餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:378]
2025-06-03 01:11:15,214 ERROR: 分析食谱时出错: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT r.id, r.name as recipe_name, mp.meal_type, mp.expected_diners
            FROM menu_plans mp
            JOIN menu_recipes mr ON mp.id = mr.menu_plan_id
            JOIN recipes r ON mr.recipe_id = r.id
            WHERE mp.area_id = ?
            AND mp.plan_date = ?
        ]
[parameters: (42, datetime.date(2025, 6, 3))]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:528]
